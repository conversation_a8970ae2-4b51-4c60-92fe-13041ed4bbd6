#!/usr/bin/env python3
"""
融合算法训练启动脚本
简化的训练入口，直接使用真实数据
"""

import os
import sys
import torch
import numpy as np
from pathlib import Path
from datetime import datetime

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 添加layers模块路径
layers_path = project_root.parent / "异常检测/Time-Series-Library-main-loss/Time-Series-Library-main"
if layers_path.exists():
    sys.path.insert(0, str(layers_path))

def main():
    """主训练函数"""
    print("=" * 80)
    print("融合算法训练启动")
    print("=" * 80)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 设置随机种子
    torch.manual_seed(42)
    np.random.seed(42)
    
    # 检查GPU
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 检查数据路径
    anomaly_data_path = project_root.parent / "异常检测/Time-Series-Library-main-loss/Time-Series-Library-main/dataset"
    earlysignal_data_path = project_root.parent / "前驱信号检测/dataset"
    
    print(f"异常检测数据路径: {anomaly_data_path}")
    print(f"前驱信号数据路径: {earlysignal_data_path}")
    
    if not anomaly_data_path.exists():
        print("⚠ 警告: 异常检测数据路径不存在")
    else:
        print("✓ 异常检测数据路径存在")
    
    if not earlysignal_data_path.exists():
        print("⚠ 警告: 前驱信号数据路径不存在")
    else:
        print("✓ 前驱信号数据路径存在")
    
    try:
        # 导入配置和实验类
        from configs.fusion_config import FusionConfig
        
        # 创建配置
        config = FusionConfig()
        
        # 调整配置用于实际训练
        config.batch_size = 8  # 减小批次大小避免内存问题
        config.train_epochs = 20  # 减少训练轮数用于快速测试
        config.learning_rate = 0.0001  # 降低学习率
        config.patience = 5  # 减少早停耐心值
        
        # 设置数据路径
        config.anomaly_data_path = str(anomaly_data_path)
        config.earlysignal_data_path = str(earlysignal_data_path)
        
        print("\n配置信息:")
        print(f"  序列长度: {config.seq_len}")
        print(f"  模型维度: {config.d_model}")
        print(f"  批次大小: {config.batch_size}")
        print(f"  学习率: {config.learning_rate}")
        print(f"  训练轮数: {config.train_epochs}")
        
        # 创建输出目录
        checkpoints_dir = project_root / "checkpoints"
        results_dir = project_root / "results"
        checkpoints_dir.mkdir(exist_ok=True)
        results_dir.mkdir(exist_ok=True)
        
        print(f"\n模型保存路径: {checkpoints_dir}")
        print(f"结果保存路径: {results_dir}")
        
        # 尝试导入实验类
        try:
            from exp.exp_fusion import Exp_Fusion
            
            print("\n" + "="*50)
            print("开始训练异常检测分支")
            print("="*50)
            
            # 训练异常检测分支
            config.task_name = 'anomaly_detection'
            exp_anomaly = Exp_Fusion(config)
            
            print("开始异常检测训练...")
            exp_anomaly.train()
            print("✓ 异常检测训练完成")
            
            print("\n" + "="*50)
            print("开始训练前驱信号检测分支")
            print("="*50)
            
            # 训练前驱信号检测分支
            config.task_name = 'earlysignaldet'
            exp_earlysignal = Exp_Fusion(config)
            
            print("开始前驱信号检测训练...")
            exp_earlysignal.train()
            print("✓ 前驱信号检测训练完成")
            
            print("\n" + "="*80)
            print("🎉 所有训练完成！")
            print("="*80)
            print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
        except ImportError as e:
            print(f"\n❌ 导入实验模块失败: {e}")
            print("使用简化训练流程...")
            
            # 使用简化的训练流程
            from models.fusion_model import FusionModel
            
            print("\n创建模型...")
            model = FusionModel(config).to(device)
            
            print(f"✓ 模型创建成功，参数数量: {sum(p.numel() for p in model.parameters()):,}")
            
            # 创建优化器
            optimizer = torch.optim.Adam(model.parameters(), lr=config.learning_rate)
            
            print("✓ 优化器创建成功")
            print("\n注意: 完整的训练需要实现数据加载器")
            print("您可以运行 python tests/quick_validation.py 验证模型功能")
            
    except Exception as e:
        print(f"\n❌ 训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        
        print("\n建议:")
        print("1. 检查数据路径是否正确")
        print("2. 确保所有依赖模块已安装")
        print("3. 运行快速验证脚本检查环境")


if __name__ == '__main__':
    main()
